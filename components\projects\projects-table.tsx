"use client";
import React, { useEffect, useState } from "react";
import {
  Input,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Button,
  Pagination,
  DateRangePicker,
  <PERSON>lt<PERSON>,
  Spinner,
} from "@heroui/react";
import { Card } from "@nextui-org/react";
import { RangeValue } from "@react-types/shared";
import { DateValue } from "@react-types/calendar";
import { useTheme } from "next-themes";
import { useRouter } from "next/navigation";
import { Icon } from "@iconify/react";
import { useQuery } from "@apollo/client";

import {
  getPhaseStyle,
  getPhaseStyleText,
  getStatusStyleText,
} from "../primitives";

import PhaseCountCard from "./phase-count-card";
import { FilterDropdown } from "./projects-table/filter-dropdown";
import { StatusModal } from "./projects-table/status-modal";

import { useAuth } from "@/hooks/auth/useAuth";
import { Project } from "@/types/projects";
import { useRowCountStore } from "@/store/use-row-count-store";
import { UserAvatar } from "@/utils/avatars";
import { GET_ALL_PROJECTS_TABLE } from "@/graphql/operations/projects";

/**
 * Maps status from English API values to Spanish UI values
 */
export const mapStatusToSpanish = (status: string): string => {
  switch (status) {
    case "IN_PROGRESS":
      return "En curso";
    case "SCHEDULED":
      return "Prevista";
    case "ON_HOLD":
      return "On hold";
    case "CANCELED":
      return "Cancelado";
    default:
      return status;
  }
};

const ProjectsTable = () => {
  const { theme } = useTheme();
  const { rowCount } = useRowCountStore();
  const router = useRouter();

  const { loading, error, data } = useQuery(GET_ALL_PROJECTS_TABLE);

  const { hasPermission } = useAuth();
  const [canModifyProjects, setCanModifyProjects] = useState(false);

  useEffect(() => {
    setCanModifyProjects(hasPermission("editar_proyectos"));
  }, [hasPermission]);

  const [projects, setProjects] = useState<Project[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredProjects, setFilteredProjects] = useState<Project[]>(projects);
  const [page, setPage] = useState(1);
  const [activeFilters, setActiveFilters] = useState<Record<string, string[]>>(
    {},
  );
  const [sortConfig, setSortConfig] = useState<{
    column: string;
    direction: "asc" | "desc";
  } | null>(null);

  const [dateRange, setDateRange] = useState<RangeValue<DateValue> | null>(
    null,
  );

  // State for modal
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<string>("");
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);

  // Function to open the modal with project details
  const openStatusModal = (project: Project) => {
    setSelectedStatus(project.estado);
    setSelectedProject(project);
    setIsModalOpen(true);
  };

  // Function to close the modal
  const closeStatusModal = () => {
    setIsModalOpen(false);
    setSelectedProject(null);
  };

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    applyFiltersAndSort(value, activeFilters, sortConfig);
  };

  const getUniqueValues = (column: keyof Project) => {
    return Array.from(
      new Set(projects.map((project) => String(project[column]))),
    );
  };

  const handleFilterChange = (column: string, selectedValues: string[]) => {
    const newFilters = {
      ...activeFilters,
      [column]: selectedValues,
    };

    if (selectedValues.length === 0) {
      delete newFilters[column];
    }

    setActiveFilters(newFilters);
    applyFiltersAndSort(searchTerm, newFilters, sortConfig);
  };

  const handleSort = (column: string, direction: "asc" | "desc") => {
    const newSortConfig = { column, direction };

    setSortConfig(newSortConfig);
    applyFiltersAndSort(searchTerm, activeFilters, newSortConfig);
  };

  const handleRange = (value: RangeValue<DateValue> | null) => {
    applyFiltersAndSort(searchTerm, activeFilters, sortConfig, value);
  };

  const handleClearFilters = () => {
    setActiveFilters({});
    setSortConfig(null);
    setDateRange(null);
    setSearchTerm("");

    // Apply changes in one go
    setFilteredProjects(projects);
    setPage(1);
  };

  const applyFiltersAndSort = (
    term: string,
    filters: Record<string, string[]>,
    sort: { column: string; direction: "asc" | "desc" } | null,
    dates: RangeValue<DateValue> | null = dateRange,
  ) => {
    let filtered = projects;

    // Apply search term filter
    if (term) {
      filtered = filtered.filter(
        (project) =>
          project.alias.toLowerCase().includes(term.toLowerCase()) ||
          project.agregador.toLowerCase().includes(term.toLowerCase()) ||
          project.lid.toString().includes(term),
      );
    }

    // Apply column filters
    Object.entries(filters).forEach(([column, values]) => {
      if (values.length > 0) {
        filtered = filtered.filter((project) =>
          values.includes(String(project[column as keyof Project])),
        );
      }
    });

    if (sort) {
      filtered = [...filtered].sort((a, b) => {
        const aValue = String(a[sort.column as keyof Project]);
        const bValue = String(b[sort.column as keyof Project]);

        if (sort.direction === "asc") {
          return aValue.localeCompare(bValue);
        } else {
          return bValue.localeCompare(aValue);
        }
      });
    }

    if (dates && dates.start && dates.end) {
      filtered = filtered.filter((project) => {
        const goLiveDate = new Date(project.goLive);

        return (
          goLiveDate >= new Date(dates.start.toString()) &&
          goLiveDate <= new Date(dates.end.toString())
        );
      });
    }

    setFilteredProjects(filtered);
    setPage(1);
  };

  const pages = Math.ceil(filteredProjects.length / rowCount);

  const items = React.useMemo(() => {
    const start = (page - 1) * rowCount;
    const end = start + rowCount;

    return filteredProjects.slice(start, end);
  }, [page, filteredProjects]);

  useEffect(() => {
    if (data && data.allProjects) {
      const fetchedProjects = data.allProjects.map((project: Partial<any>) => {
        // Parse percentages array to extract individual phase percentages
        const percentagesMap: Record<string, number> = {};
        let totalPercentage = 0;

        if (project.percentages && Array.isArray(project.percentages)) {
          project.percentages.forEach((item: string) => {
            try {
              const parsed = JSON.parse(item);

              percentagesMap[parsed.name] = parsed.percentage;
              totalPercentage += parsed.percentage;
            } catch (e) {
              // Log error silently
            }
          });
        }

        // Calculate total percentage (average of all phases)
        totalPercentage = project.percentages?.length
          ? Math.round(totalPercentage / project.percentages.length)
          : 0;

        return {
          lid: project.lid || "N/A",
          alias: project.alias || "N/A",
          agregador: project.aggregator || "N/A",
          tipologia: project.implementationType || "N/A",
          implementacion:
            project.implementer1?.firstName +
              " " +
              project.implementer1?.lastName || "N/A",
          goLive: project.goliveFinalDate
            ? project.goliveFinalDate.substring(0, 7) // Transform YYYY-MM-DD to YYYY-MM
            : "N/A",
          total: totalPercentage,
          s: percentagesMap["START"] || 0,
          c: percentagesMap["COLLECTION"] || 0,
          m: percentagesMap["MIGRATION"] || 0,
          t: percentagesMap["TEST"] || 0,
          g: percentagesMap["GO LIVE"] || 0,
          i: percentagesMap["INCUBADORA"] || 0,
          fase: project.actualPhase || "N/A",
          estado: project.status ? mapStatusToSpanish(project.status) : "N/A",
          id: project.id || -1,
        };
      });

      setProjects(fetchedProjects);
      setFilteredProjects(fetchedProjects);
    }
  }, [data]);

  useEffect(() => {
    if (!dateRange) return;
    handleRange(dateRange);
  }, [dateRange]);

  return (
    <div className="w-full pt-4">
      <Card className="w-full p-2 mb-4 pb-4" radius={"sm"}>
        <div className="flex flex-col sm:flex-row gap-4 justify-between items-center mb-4">
          <div className="flex gap-2 w-full sm:w-auto">
            <Input
              className="w-full sm:w-96"
              placeholder="Buscar por LID, ALIAS o AGREGADOR..."
              startContent={"🔍"}
              value={searchTerm}
              onValueChange={handleSearch}
            />
          </div>
          <div className="flex gap-2 w-full sm:w-auto justify-end">
            <DateRangePicker
              showMonthAndYearPickers
              className="w-min"
              value={dateRange}
              visibleMonths={2}
              onChange={(value) => setDateRange(value)}
            />

            <Button
              color={
                Object.keys(activeFilters).length > 0 || dateRange || searchTerm
                  ? "primary"
                  : "default"
              }
              variant="flat"
              onPress={handleClearFilters}
            >
              Borrar filtros{" "}
              {(Object.keys(activeFilters).length > 0 ||
                dateRange ||
                searchTerm) &&
                `(${
                  Object.keys(activeFilters).length +
                  (dateRange ? 1 : 0) +
                  (searchTerm ? 1 : 0)
                })`}
            </Button>
          </div>
        </div>
        <div className={"w-full flex flex-col items-center"}>
          <div className="w-7/12 flex flex-col sm:flex-row gap-4 justify-between items-center">
            <PhaseCountCard phase="START" projects={projects} />
            <PhaseCountCard phase="COLLECTION" projects={projects} />
            <PhaseCountCard phase="MIGRATION" projects={projects} />
            <PhaseCountCard phase="TEST" projects={projects} />
            <PhaseCountCard phase="GO LIVE" projects={projects} />
            <PhaseCountCard phase="INCUBADORA" projects={projects} />
            <PhaseCountCard phase="TOTAL" projects={projects} />
          </div>
        </div>
      </Card>

      <Table
        key={theme}
        removeWrapper
        aria-label="Projects table"
        bottomContent={
          <div className="flex w-full justify-center">
            <Pagination
              isCompact
              showControls
              showShadow
              color="primary"
              page={page}
              total={pages}
              onChange={(page) => setPage(page)}
            />
          </div>
        }
      >
        <TableHeader>
          <TableColumn className="w-[3%] cursor-pointer">
            {/* {filterDropdown("ID", "id")} */}
            <FilterDropdown
              activeFilters={activeFilters}
              column={"lid"}
              items={getUniqueValues("lid")}
              sortConfig={sortConfig}
              title={"LID"}
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn className="w-[10%] cursor-pointer">
            <FilterDropdown
              activeFilters={activeFilters}
              column={"alias"}
              items={getUniqueValues("alias")}
              sortConfig={sortConfig}
              title={"ALIAS"}
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn className="w-[10%] cursor-pointer">
            <FilterDropdown
              activeFilters={activeFilters}
              column={"agregador"}
              items={getUniqueValues("agregador")}
              sortConfig={sortConfig}
              title={"AGREGADOR"}
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn className="w-[8%] cursor-pointer">
            <FilterDropdown
              activeFilters={activeFilters}
              column={"tipologia"}
              items={getUniqueValues("tipologia")}
              sortConfig={sortConfig}
              title={"TIPOLOGIA"}
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn className="w-[6%] cursor-pointer">
            <FilterDropdown
              activeFilters={activeFilters}
              column={"implementacion"}
              items={getUniqueValues("implementacion")}
              sortConfig={sortConfig}
              title={"IMPL 1"}
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn className="w-[8%] cursor-pointer">
            <FilterDropdown
              activeFilters={activeFilters}
              column={"goLive"}
              items={getUniqueValues("goLive")}
              sortConfig={sortConfig}
              title={"GO LIVE"}
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn className="w-[5%] cursor-pointer">
            <FilterDropdown
              activeFilters={activeFilters}
              column={"total"}
              items={getUniqueValues("total")}
              sortConfig={sortConfig}
              title={"TOTAL"}
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn className="w-[3%] cursor-pointer">
            <FilterDropdown
              activeFilters={activeFilters}
              column={"s"}
              items={getUniqueValues("s")}
              sortConfig={sortConfig}
              title={"S"}
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn className="w-[3%] cursor-pointer">
            <FilterDropdown
              activeFilters={activeFilters}
              column={"c"}
              items={getUniqueValues("c")}
              sortConfig={sortConfig}
              title={"C"}
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn className="w-[3%] cursor-pointer">
            <FilterDropdown
              activeFilters={activeFilters}
              column={"m"}
              items={getUniqueValues("m")}
              sortConfig={sortConfig}
              title={"M"}
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn className="w-[3%] cursor-pointer">
            <FilterDropdown
              activeFilters={activeFilters}
              column={"t"}
              items={getUniqueValues("t")}
              sortConfig={sortConfig}
              title={"T"}
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn className="w-[3%] cursor-pointer">
            <FilterDropdown
              activeFilters={activeFilters}
              column={"g"}
              items={getUniqueValues("g")}
              sortConfig={sortConfig}
              title={"G"}
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn className="w-[3%] cursor-pointer">
            <FilterDropdown
              activeFilters={activeFilters}
              column={"i"}
              items={getUniqueValues("i")}
              sortConfig={sortConfig}
              title={"I"}
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn className="w-[5%] cursor-pointer">
            <FilterDropdown
              activeFilters={activeFilters}
              column={"fase"}
              items={getUniqueValues("fase")}
              sortConfig={sortConfig}
              title={"FASE"}
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn className="w-[3%] cursor-pointer">
            <FilterDropdown
              activeFilters={activeFilters}
              column={"estado"}
              items={getUniqueValues("estado")}
              sortConfig={sortConfig}
              title={"ESTADO"}
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn className="w-[3%] cursor-pointer">#</TableColumn>
        </TableHeader>
        <TableBody
          emptyContent={"No se han encontrado proyectos (╥_╥)"}
          isLoading={loading}
          items={items}
          loadingContent={<Spinner label="Cargando proyectos..." />}
        >
          {(item) => (
            <TableRow
              key={item.id}
              className="cursor-pointer hover:bg-default-100"
              onClick={() => (
                router.push(`/proyecto/${item.id}`), window.scrollTo(0, 0)
              )}
            >
              <TableCell className="max-w-[5%] whitespace-nowrap overflow-hidden text-ellipsis">
                {item.lid}
              </TableCell>
              <TableCell className="max-w-[10%] whitespace-nowrap overflow-hidden text-ellipsis">
                {item.alias}
              </TableCell>
              <TableCell className="max-w-[10%] whitespace-nowrap overflow-hidden text-ellipsis">
                {item.agregador}
              </TableCell>
              <TableCell className="max-w-[8%] whitespace-nowrap overflow-hidden text-ellipsis">
                {item.tipologia}
              </TableCell>
              <TableCell className="max-w-[10%] whitespace-nowrap overflow-hidden text-ellipsis">
                <Tooltip
                  content={
                    <div className="flex flex-col items-center">
                      <h3 className="text-sm font-medium text-default-700">
                        {item.implementacion}
                      </h3>
                    </div>
                  }
                >
                  {UserAvatar(item.implementacion, theme)}
                </Tooltip>
              </TableCell>

              <TableCell className="max-w-[8%] whitespace-nowrap overflow-hidden text-ellipsis">
                {item.goLive}
              </TableCell>
              <TableCell className="max-w-[12%] whitespace-nowrap overflow-hidden text-ellipsis">
                {item.total}%
              </TableCell>
              <TableCell className="max-w-[3%] whitespace-nowrap overflow-hidden text-ellipsis">
                {item.s}%
              </TableCell>
              <TableCell className="max-w-[3%] whitespace-nowrap overflow-hidden text-ellipsis">
                {item.c}%
              </TableCell>
              <TableCell className="max-w-[3%] whitespace-nowrap overflow-hidden text-ellipsis">
                {item.m}%
              </TableCell>
              <TableCell className="max-w-[3%] whitespace-nowrap overflow-hidden text-ellipsis">
                {item.t}%
              </TableCell>
              <TableCell className="max-w-[3%] whitespace-nowrap overflow-hidden text-ellipsis">
                {item.g}%
              </TableCell>
              <TableCell className="max-w-[3%] whitespace-nowrap overflow-hidden text-ellipsis text-left">
                {item.i}%
              </TableCell>
              <TableCell className="max-w-[5%] whitespace-nowrap overflow-hidden text-ellipsis">
                <div
                  className={`text-center p-1 rounded-lg ${getPhaseStyle(
                    item.fase,
                    theme === "dark",
                  )} ${getPhaseStyleText(item.fase, theme === "dark")}`}
                >
                  {item.fase}
                </div>
              </TableCell>
              <TableCell
                className={`max-w-[3%] whitespace-nowrap overflow-hidden text-ellipsis text-center ${getStatusStyleText(item.estado, theme === "dark")}`}
              >
                <div className="flex justify-center">
                  {item.estado === "Prevista" ? (
                    <Icon icon="lucide:clock" width={18} />
                  ) : item.estado === "En curso" ? (
                    <Icon icon="lucide:play-circle" width={18} />
                  ) : item.estado === "On hold" ? (
                    <Icon icon="lucide:pause-circle" width={18} />
                  ) : item.estado === "Cancelado" ? (
                    <Icon icon="lucide:x-circle" width={18} />
                  ) : (
                    <Icon icon="lucide:circle" width={18} />
                  )}
                </div>
              </TableCell>
              <TableCell className="max-w-[3%] whitespace-nowrap overflow-hidden text-ellipsis">
                <Button
                  isIconOnly
                  color="primary"
                  isDisabled={!canModifyProjects}
                  size="sm"
                  variant="flat"
                  onPress={() => {
                    openStatusModal(item);

                    return false;
                  }}
                >
                  <Icon icon="lucide:edit-3" width={18} />
                </Button>
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>

      <StatusModal
        isOpen={isModalOpen}
        selectedProject={selectedProject}
        setIsOpen={setIsModalOpen}
        onClose={closeStatusModal}
      />
    </div>
  );
};

export default ProjectsTable;
