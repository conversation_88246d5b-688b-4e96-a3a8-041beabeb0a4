import type { Role } from "@/types/role";

import React from "react";
import { <PERSON><PERSON> } from "@heroui/react";
import { Icon } from "@iconify/react";

interface RoleActionsProps {
  role: Role;
  onEdit: (role: Role) => void;
  onDelete: (role: Role) => void;
  canEditRoles?: boolean;
}

export function RoleActions({ role, onEdit, onDelete, canEditRoles = false }: RoleActionsProps) {
  return (
    <div className="flex gap-2 justify-end">
      <Button
        isIconOnly
        isDisabled={!canEditRoles}
        color="primary"
        size="sm"
        startContent={<Icon className="text-lg" icon="lucide:edit" />}
        variant="flat"
        onPress={() => onEdit(role)}
      />
      <Button
        isIconOnly
        isDisabled={!canEditRoles}
        color="danger"
        size="sm"
        startContent={<Icon className="text-lg" icon="lucide:trash" />}
        variant="flat"
        onPress={() => onDelete(role)}
      />
    </div>
  );
}
