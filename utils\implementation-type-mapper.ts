import { ProjectsProjectImplementationTypeChoices } from "@/graphql/schemas/generated";

/**
 * Maps implementation type display names to GraphQL enum values
 * @param typeName - The display name of the implementation type (e.g., "Nueva", "Existente")
 * @returns The corresponding GraphQL enum value (e.g., "NUEVA", "EXISTENTE")
 */
export const mapImplementationTypeToEnum = (
  typeName: string,
): ProjectsProjectImplementationTypeChoices => {
  const typeMap: Record<string, ProjectsProjectImplementationTypeChoices> = {
    Nueva: ProjectsProjectImplementationTypeChoices.Nueva,
    Existente: ProjectsProjectImplementationTypeChoices.Existente,
    Migración: ProjectsProjectImplementationTypeChoices.Migracion,
    Subrogación: ProjectsProjectImplementationTypeChoices.Subrogacion,
  };

  return typeMap[typeName] || ProjectsProjectImplementationTypeChoices.Nueva;
};

/**
 * Maps GraphQL enum values to display names
 * @param enumValue - The GraphQL enum value (e.g., "NUEVA", "EXISTENTE")
 * @returns The corresponding display name (e.g., "<PERSON>ueva", "Existente")
 */
export const mapEnumToImplementationType = (
  enumValue: ProjectsProjectImplementationTypeChoices,
): string => {
  const enumMap: Record<ProjectsProjectImplementationTypeChoices, string> = {
    [ProjectsProjectImplementationTypeChoices.Nueva]: "Nueva",
    [ProjectsProjectImplementationTypeChoices.Existente]: "Existente",
    [ProjectsProjectImplementationTypeChoices.Migracion]: "Migración",
    [ProjectsProjectImplementationTypeChoices.Subrogacion]: "Subrogación",
  };

  return enumMap[enumValue] || "Nueva";
};
